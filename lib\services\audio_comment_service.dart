import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/audio_comment.dart';

class AudioCommentService {
  final SupabaseClient _client = Supabase.instance.client;

  String? get currentUserId => _client.auth.currentUser?.id;

  // =============================================================
  //  التعليقات النصية
  // =============================================================

  /// إضافة تعليق نصي
  Future<String> addTextComment({
    required String episodeId,
    required String content,
    String? parentCommentId,
  }) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    final data = await _client.from('audio_episode_comments').insert({
      'episode_id': episodeId,
      'user_id': userId,
      'content': content,
      'parent_comment_id': parentCommentId,
    }).select().single();

    return data['id'];
  }

  /// تحديث تعليق نصي
  Future<void> updateTextComment(String commentId, String newContent) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    await _client.from('audio_episode_comments')
        .update({
          'content': newContent,
          'is_edited': true,
          'updated_at': DateTime.now().toIso8601String(),
        })
        .eq('id', commentId)
        .eq('user_id', userId);
  }

  /// حذف تعليق نصي
  Future<void> deleteTextComment(String commentId) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    await _client.from('audio_episode_comments')
        .delete()
        .eq('id', commentId)
        .eq('user_id', userId);
  }

  /// جلب التعليقات النصية
  Future<List<AudioEpisodeComment>> fetchTextComments(String episodeId) async {
    final response = await _client
        .from('audio_episode_comments')
        .select('''
          *,
          profiles:user_id (
            name,
            avatar_url,
            is_verified
          )
        ''')
        .eq('episode_id', episodeId)
        .isFilter('parent_comment_id', null)
        .order('created_at', ascending: false);

    final comments = (response as List).map((json) => 
        AudioEpisodeComment.fromJson(json, currentUserId: currentUserId)).toList();

    // جلب الردود لكل تعليق
    for (int i = 0; i < comments.length; i++) {
      final replies = await _fetchCommentReplies(comments[i].id);
      comments[i] = comments[i].copyWith(replies: replies);
    }

    return comments;
  }

  /// جلب ردود التعليق
  Future<List<AudioEpisodeComment>> _fetchCommentReplies(String parentCommentId) async {
    final response = await _client
        .from('audio_episode_comments')
        .select('''
          *,
          profiles:user_id (
            name,
            avatar_url,
            is_verified
          )
        ''')
        .eq('parent_comment_id', parentCommentId)
        .order('created_at', ascending: true);

    return (response as List).map((json) => 
        AudioEpisodeComment.fromJson(json, currentUserId: currentUserId)).toList();
  }

  /// إعجاب بتعليق نصي
  Future<void> likeTextComment(String commentId) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    await _client.from('audio_comment_likes').insert({
      'comment_id': commentId,
      'user_id': userId,
    });
  }

  /// إلغاء إعجاب بتعليق نصي
  Future<void> unlikeTextComment(String commentId) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    await _client.from('audio_comment_likes')
        .delete()
        .eq('comment_id', commentId)
        .eq('user_id', userId);
  }

  /// تبديل إعجاب بتعليق نصي
  Future<bool> toggleTextCommentLike(String commentId) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    final existing = await _client
        .from('audio_comment_likes')
        .select('id')
        .eq('comment_id', commentId)
        .eq('user_id', userId)
        .maybeSingle();

    if (existing != null) {
      await unlikeTextComment(commentId);
      return false;
    } else {
      await likeTextComment(commentId);
      return true;
    }
  }

  // =============================================================
  //  التعليقات الصوتية
  // =============================================================

  /// إضافة تعليق صوتي
  Future<String> addVoiceComment({
    required String episodeId,
    required String audioPath,
    required int durationSeconds,
    String? transcript,
  }) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    // رفع الملف الصوتي
    final audioUrl = await _uploadVoiceComment(audioPath, userId);

    final data = await _client.from('audio_episode_voice_comments').insert({
      'episode_id': episodeId,
      'user_id': userId,
      'audio_url': audioUrl,
      'duration_seconds': durationSeconds,
      'transcript': transcript,
    }).select().single();

    return data['id'];
  }

  /// حذف تعليق صوتي
  Future<void> deleteVoiceComment(String commentId) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    await _client.from('audio_episode_voice_comments')
        .delete()
        .eq('id', commentId)
        .eq('user_id', userId);
  }

  /// جلب التعليقات الصوتية
  Future<List<AudioEpisodeVoiceComment>> fetchVoiceComments(String episodeId) async {
    final response = await _client
        .from('audio_episode_voice_comments')
        .select('''
          *,
          profiles:user_id (
            name,
            avatar_url,
            is_verified
          )
        ''')
        .eq('episode_id', episodeId)
        .order('created_at', ascending: false);

    return (response as List).map((json) => 
        AudioEpisodeVoiceComment.fromJson(json, currentUserId: currentUserId)).toList();
  }

  /// إعجاب بتعليق صوتي
  Future<void> likeVoiceComment(String commentId) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    await _client.from('audio_comment_likes').insert({
      'voice_comment_id': commentId,
      'user_id': userId,
    });
  }

  /// إلغاء إعجاب بتعليق صوتي
  Future<void> unlikeVoiceComment(String commentId) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    await _client.from('audio_comment_likes')
        .delete()
        .eq('voice_comment_id', commentId)
        .eq('user_id', userId);
  }

  /// تبديل إعجاب بتعليق صوتي
  Future<bool> toggleVoiceCommentLike(String commentId) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    final existing = await _client
        .from('audio_comment_likes')
        .select('id')
        .eq('voice_comment_id', commentId)
        .eq('user_id', userId)
        .maybeSingle();

    if (existing != null) {
      await unlikeVoiceComment(commentId);
      return false;
    } else {
      await likeVoiceComment(commentId);
      return true;
    }
  }

  // =============================================================
  //  دوال مساعدة
  // =============================================================

  /// رفع تعليق صوتي
  Future<String> _uploadVoiceComment(String audioPath, String userId) async {
    final file = File(audioPath);
    final fileName = '${userId}/comment_${DateTime.now().millisecondsSinceEpoch}.mp3';
    
    await _client.storage.from('voice-comments').upload(fileName, file);
    return _client.storage.from('voice-comments').getPublicUrl(fileName);
  }

  /// جلب عدد التعليقات الإجمالي للحلقة
  Future<int> getEpisodeCommentsCount(String episodeId) async {
    final textCommentsResponse = await _client
        .from('audio_episode_comments')
        .select('id')
        .eq('episode_id', episodeId);

    final voiceCommentsResponse = await _client
        .from('audio_episode_voice_comments')
        .select('id')
        .eq('episode_id', episodeId);

    return textCommentsResponse.length + voiceCommentsResponse.length;
  }

  /// البحث في التعليقات
  Future<List<AudioEpisodeComment>> searchComments(String episodeId, String query) async {
    final response = await _client
        .from('audio_episode_comments')
        .select('''
          *,
          profiles:user_id (
            name,
            avatar_url,
            is_verified
          )
        ''')
        .eq('episode_id', episodeId)
        .ilike('content', '%$query%')
        .order('created_at', ascending: false);

    return (response as List).map((json) => 
        AudioEpisodeComment.fromJson(json, currentUserId: currentUserId)).toList();
  }
}
