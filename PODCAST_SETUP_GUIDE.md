# 🎙️ دليل إعداد قسم البودكاست - تطبيق أرزاوو

## 🚨 حل مشكلة "relation does not exist"

إذا ظهرت لك رسالة خطأ مثل:
```
PostgrestException (message: relation "public.featured_audio_podcasts" does not exist, code: 42P01, details: Not Found, hint: null)
```

فهذا يعني أن قاعدة البيانات تحتاج إلى إعداد جداول البودكاست.

## 📋 خطوات الإعداد

### الخطوة 1: إعداد قاعدة البيانات
1. اذهب إلى **Supabase Dashboard**
2. افتح **SQL Editor**
3. انسخ والصق محتوى ملف `database/basic_podcast_setup.sql`
4. اضغط **Run** لتنفيذ الاستعلامات

### الخطوة 2: إعداد Storage
1. في نفس **SQL Editor**
2. انسخ والصق محتوى ملف `database/podcast_storage_setup.sql`
3. اضغط **Run** لتنفيذ الاستعلامات

### الخطوة 3: التحقق من الإعداد
بعد تنفيذ الملفين، يجب أن ترى رسائل نجاح:
- ✅ "تم إعداد قسم البودكاست بنجاح!"
- ✅ "تم إعداد Storage للبودكاست بنجاح!"

### الخطوة 4: اختبار التطبيق
1. افتح التطبيق الجديد
2. اضغط على زر **الزائد** ➕ في الشريط السفلي
3. اختر **بودكاست** 🎙️ من القائمة المنبثقة
4. يجب أن تفتح صفحة البودكاست بدون أخطاء

## 🎯 المميزات المتوفرة بعد الإعداد

### للمنشئين:
- ✅ **إنشاء بودكاست جديد** مع صورة غلاف ووصف
- ✅ **تسجيل حلقات صوتية** مباشرة من التطبيق 🎤
- ✅ **رفع ملفات صوتية** من الجهاز 📁
- ✅ **جدولة النشر** للحلقات
- ✅ **إعدادات الخصوصية** والتحكم في التعليقات

### للمستمعين:
- ✅ **تشغيل متقدم** مع تحكم كامل
- ✅ **التعليقات الصوتية والنصية** 💬
- ✅ **الإعجاب والمشاركة** ❤️
- ✅ **المتابعة والإشعارات** 🔔
- ✅ **البحث والفلترة** 🔍

## 🗂️ الجداول المُنشأة

### الجداول الأساسية:
- `audio_podcasts` - البودكاستات
- `audio_episodes` - الحلقات الصوتية
- `audio_podcast_followers` - المتابعون
- `audio_podcast_likes` - الإعجابات
- `audio_episode_likes` - إعجابات الحلقات
- `audio_episode_listens` - إحصائيات الاستماع
- `audio_episode_text_comments` - التعليقات النصية
- `audio_episode_voice_comments` - التعليقات الصوتية

### Storage Buckets:
- `audio-episodes` - الملفات الصوتية (خاص)
- `podcast-covers` - صور الأغلفة (عام)
- `voice-comments` - التعليقات الصوتية (خاص)

## 🔒 الأمان والصلاحيات

### سياسات الأمان (RLS):
- ✅ المستخدمون يمكنهم رؤية البودكاستات العامة فقط
- ✅ المنشئون يمكنهم إدارة بودكاستاتهم فقط
- ✅ المستخدمون يمكنهم إدارة متابعاتهم وإعجاباتهم
- ✅ التعليقات مرئية للجميع، قابلة للتعديل من المالك فقط

### صلاحيات Storage:
- ✅ رفع الملفات للمستخدمين المسجلين فقط
- ✅ حذف الملفات لمالك الملف فقط
- ✅ صور الأغلفة مرئية للجميع
- ✅ الملفات الصوتية محمية

## 🔧 استكشاف الأخطاء

### إذا استمر ظهور أخطاء:
1. **تأكد من تنفيذ جميع الاستعلامات** في SQL Editor
2. **تحقق من وجود الجداول** في Database > Tables
3. **تأكد من وجود Buckets** في Storage
4. **أعد تشغيل التطبيق** بعد الإعداد

### إذا لم تظهر البيانات:
1. **أنشئ بودكاست تجريبي** من التطبيق
2. **تحقق من الصلاحيات** في Authentication > Users
3. **راجع سياسات RLS** في Database > Policies

## 📱 كيفية الوصول لقسم البودكاست

### من التطبيق:
1. اضغط على زر **الزائد** ➕ في الشريط السفلي
2. اختر **بودكاست** 🎙️ من القائمة المنبثقة
3. أو استخدم زر **الميكروفون** 🎤 في صفحة البودكاست

### الأقسام الستة في الشريط السفلي:
1. **الرئيسية** 🏠 (الخلاصة)
2. **المستخدمين** 👥 
3. **نبض** 📊 (التصويتات)
4. **مساحتي** 🏢 (المساحات الشخصية)
5. **الفيديو** 🎥
6. **المجتمع** 👨‍👩‍👧‍👦

## 🎉 تم الإعداد بنجاح!

بعد اتباع هذه الخطوات، سيعمل قسم البودكاست بشكل كامل مع جميع المميزات المتقدمة.

**ملاحظة**: إذا واجهت أي مشاكل، تأكد من أن المستخدم لديه صلاحيات كافية في Supabase وأن جميع الاستعلامات تم تنفيذها بنجاح.
