import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/audio_podcast.dart';
import '../models/audio_episode.dart';
import '../models/audio_podcast_category.dart';

class AudioPodcastService {
  final SupabaseClient _client = Supabase.instance.client;

  String? get currentUserId => _client.auth.currentUser?.id;

  // =============================================================
  //  إدارة البودكاستات
  // =============================================================

  /// إنشاء بودكاست جديد
  Future<String> createPodcast({
    required String name,
    String? description,
    required String category,
    String language = 'ar',
    String? coverImagePath,
    AudioPodcastPrivacy privacy = AudioPodcastPrivacy.public,
    bool allowComments = true,
    bool allowDownloads = true,
    AudioContentRating contentRating = AudioContentRating.general,
  }) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    String? coverImageUrl;
    if (coverImagePath != null) {
      coverImageUrl = await _uploadCoverImage(coverImagePath, userId);
    }

    final data = await _client.from('audio_podcasts').insert({
      'creator_id': userId,
      'name': name,
      'description': description,
      'category': category,
      'language': language,
      'cover_image_url': coverImageUrl,
      'privacy_setting': privacy.value,
      'allow_comments': allowComments,
      'allow_downloads': allowDownloads,
      'content_rating': contentRating.value,
    }).select().single();

    return data['id'];
  }

  /// تحديث بودكاست
  Future<void> updatePodcast(String podcastId, {
    String? name,
    String? description,
    String? category,
    String? language,
    String? coverImagePath,
    AudioPodcastPrivacy? privacy,
    bool? allowComments,
    bool? allowDownloads,
    AudioContentRating? contentRating,
  }) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    final updateData = <String, dynamic>{};
    
    if (name != null) updateData['name'] = name;
    if (description != null) updateData['description'] = description;
    if (category != null) updateData['category'] = category;
    if (language != null) updateData['language'] = language;
    if (privacy != null) updateData['privacy_setting'] = privacy.value;
    if (allowComments != null) updateData['allow_comments'] = allowComments;
    if (allowDownloads != null) updateData['allow_downloads'] = allowDownloads;
    if (contentRating != null) updateData['content_rating'] = contentRating.value;

    if (coverImagePath != null) {
      final coverImageUrl = await _uploadCoverImage(coverImagePath, userId);
      updateData['cover_image_url'] = coverImageUrl;
    }

    await _client.from('audio_podcasts')
        .update(updateData)
        .eq('id', podcastId)
        .eq('creator_id', userId);
  }

  /// حذف بودكاست
  Future<void> deletePodcast(String podcastId) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    await _client.from('audio_podcasts')
        .delete()
        .eq('id', podcastId)
        .eq('creator_id', userId);
  }

  /// جلب البودكاستات
  Future<List<AudioPodcast>> fetchPodcasts({
    AudioPodcastSearchFilters? filters,
  }) async {
    try {
      var query = _client.from('audio_podcasts_with_stats').select('*');

      if (filters != null) {
        if (filters.hasQuery) {
          query = query.or('name.ilike.%${filters.query}%,description.ilike.%${filters.query}%');
        }
        if (filters.categoryId != null) {
          query = query.eq('category', filters.categoryId!);
        }
        if (filters.language != null) {
          query = query.eq('language', filters.language!);
        }
        if (filters.contentRating != null) {
          query = query.eq('content_rating', filters.contentRating!.value);
        }

        // تطبيق الترتيب والحد
        String orderColumn = 'created_at';
        bool ascending = false;

        switch (filters.sortBy) {
          case AudioPodcastSortBy.popularity:
            orderColumn = 'total_listens';
            break;
          case AudioPodcastSortBy.newest:
            orderColumn = 'created_at';
            break;
          case AudioPodcastSortBy.oldest:
            orderColumn = 'created_at';
            ascending = true;
            break;
          case AudioPodcastSortBy.mostFollowed:
            orderColumn = 'total_followers';
            break;
          case AudioPodcastSortBy.mostLiked:
            orderColumn = 'total_likes';
            break;
          case AudioPodcastSortBy.alphabetical:
            orderColumn = 'name';
            ascending = true;
            break;
        }

        final response = await query
            .order(orderColumn, ascending: ascending)
            .range(filters.offset, filters.offset + filters.limit - 1);

        return (response as List).map((json) => AudioPodcast.fromJson(json, currentUserId: currentUserId)).toList();
      } else {
        final response = await query.order('total_listens', ascending: false).limit(20);
        return (response as List).map((json) => AudioPodcast.fromJson(json, currentUserId: currentUserId)).toList();
      }
    } catch (e) {
      print('❌ خطأ في جلب البودكاستات: $e');
      return [];
    }
  }

  /// جلب بودكاست واحد
  Future<AudioPodcast?> fetchPodcast(String podcastId) async {
    try {
      // محاولة استخدام الجدول المتقدم أولاً
      final response = await _client
          .from('audio_podcasts_with_stats')
          .select('*')
          .eq('id', podcastId)
          .maybeSingle();
      if (response == null) return null;
      return AudioPodcast.fromJson(response, currentUserId: currentUserId);
    } catch (e) {
      // إذا فشل، استخدم الجدول الأساسي
      final response = await _client
          .from('audio_podcasts')
          .select('*')
          .eq('id', podcastId)
          .maybeSingle();
      if (response == null) return null;
      return AudioPodcast.fromJson(response, currentUserId: currentUserId);
    }
  }

  /// جلب البودكاستات المميزة
  Future<List<AudioPodcast>> fetchFeaturedPodcasts() async {
    try {
      // محاولة استخدام الجدول المتقدم أولاً
      final response = await _client
          .from('featured_audio_podcasts')
          .select('*')
          .limit(10);
      return (response as List).map((json) => AudioPodcast.fromJson(json, currentUserId: currentUserId)).toList();
    } catch (e) {
      // إذا فشل، استخدم الجدول الأساسي
      return await fetchPopularPodcasts();
    }
  }

  /// جلب أشهر البودكاستات
  Future<List<AudioPodcast>> fetchPopularPodcasts() async {
    try {
      // محاولة استخدام الجدول المتقدم أولاً
      final response = await _client
          .from('popular_audio_podcasts')
          .select('*')
          .limit(20);
      return (response as List).map((json) => AudioPodcast.fromJson(json, currentUserId: currentUserId)).toList();
    } catch (e) {
      // إذا فشل، استخدم الجدول الأساسي
      final response = await _client
          .from('audio_podcasts')
          .select('*')
          .order('created_at', ascending: false)
          .limit(20);
      return (response as List).map((json) => AudioPodcast.fromJson(json, currentUserId: currentUserId)).toList();
    }
  }

  /// جلب بودكاستات المستخدم
  Future<List<AudioPodcast>> fetchUserPodcasts(String userId) async {
    try {
      // محاولة استخدام الجدول المتقدم أولاً
      final response = await _client
          .from('audio_podcasts_with_stats')
          .select('*')
          .eq('creator_id', userId)
          .order('created_at', ascending: false);
      return (response as List).map((json) => AudioPodcast.fromJson(json, currentUserId: currentUserId)).toList();
    } catch (e) {
      // إذا فشل، استخدم الجدول الأساسي
      final response = await _client
          .from('audio_podcasts')
          .select('*')
          .eq('creator_id', userId)
          .order('created_at', ascending: false);
      return (response as List).map((json) => AudioPodcast.fromJson(json, currentUserId: currentUserId)).toList();
    }
  }

  // =============================================================
  //  إدارة الحلقات
  // =============================================================

  /// إضافة حلقة جديدة
  Future<String> addEpisode({
    required String podcastId,
    required String title,
    String? description,
    required String audioPath,
    int durationSeconds = 0,
    bool publishNow = true,
    DateTime? scheduledFor,
  }) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    // رفع الملف الصوتي
    final audioUrl = await _uploadAudioFile(audioPath, userId, podcastId);
    
    // حساب حجم الملف
    final file = File(audioPath);
    final fileSizeBytes = await file.length();

    final data = await _client.from('audio_episodes').insert({
      'podcast_id': podcastId,
      'title': title,
      'description': description,
      'audio_url': audioUrl,
      'duration_seconds': durationSeconds,
      'file_size_bytes': fileSizeBytes,
      'is_published': publishNow,
      'publish_at': publishNow ? DateTime.now().toIso8601String() : null,
      'scheduled_for': scheduledFor?.toIso8601String(),
    }).select().single();

    final episodeId = data['id'];

    // إرسال إشعارات للمتابعين إذا تم النشر فوراً
    if (publishNow) {
      await _notifyFollowersNewEpisode(episodeId);
    }

    return episodeId;
  }

  /// تحديث حلقة
  Future<void> updateEpisode(String episodeId, {
    String? title,
    String? description,
    String? audioPath,
    int? durationSeconds,
    bool? isPublished,
    DateTime? publishAt,
    DateTime? scheduledFor,
  }) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    final updateData = <String, dynamic>{};
    
    if (title != null) updateData['title'] = title;
    if (description != null) updateData['description'] = description;
    if (durationSeconds != null) updateData['duration_seconds'] = durationSeconds;
    if (isPublished != null) updateData['is_published'] = isPublished;
    if (publishAt != null) updateData['publish_at'] = publishAt.toIso8601String();
    if (scheduledFor != null) updateData['scheduled_for'] = scheduledFor.toIso8601String();

    if (audioPath != null) {
      // الحصول على معلومات الحلقة للحصول على podcast_id
      final episode = await _client
          .from('audio_episodes')
          .select('podcast_id')
          .eq('id', episodeId)
          .single();
      
      final audioUrl = await _uploadAudioFile(audioPath, userId, episode['podcast_id']);
      updateData['audio_url'] = audioUrl;
      
      final file = File(audioPath);
      updateData['file_size_bytes'] = await file.length();
    }

    await _client.from('audio_episodes')
        .update(updateData)
        .eq('id', episodeId);
  }

  /// حذف حلقة
  Future<void> deleteEpisode(String episodeId) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    await _client.from('audio_episodes').delete().eq('id', episodeId);
  }

  /// جلب حلقات بودكاست
  Future<List<AudioEpisode>> fetchEpisodes(String podcastId) async {
    final response = await _client
        .from('audio_episodes_with_podcast')
        .select('*')
        .eq('podcast_id', podcastId)
        .order('publish_at', ascending: false);

    return (response as List).map((json) => AudioEpisode.fromJson(json, currentUserId: currentUserId)).toList();
  }

  /// جلب أحدث الحلقات
  Future<List<AudioEpisode>> fetchLatestEpisodes({int limit = 20}) async {
    try {
      // محاولة استخدام الجدول المتقدم أولاً
      final response = await _client
          .from('latest_audio_episodes')
          .select('*')
          .limit(limit);
      return (response as List).map((json) => AudioEpisode.fromJson(json, currentUserId: currentUserId)).toList();
    } catch (e) {
      // إذا فشل، استخدم الجدول الأساسي
      final response = await _client
          .from('audio_episodes')
          .select('*')
          .order('created_at', ascending: false)
          .limit(limit);
      return (response as List).map((json) => AudioEpisode.fromJson(json, currentUserId: currentUserId)).toList();
    }
  }

  /// جلب حلقة واحدة
  Future<AudioEpisode?> fetchEpisode(String episodeId) async {
    final response = await _client
        .from('audio_episodes_with_podcast')
        .select('*')
        .eq('id', episodeId)
        .maybeSingle();

    if (response == null) return null;
    return AudioEpisode.fromJson(response, currentUserId: currentUserId);
  }

  // =============================================================
  //  دوال مساعدة خاصة
  // =============================================================

  /// رفع صورة الغلاف
  Future<String> _uploadCoverImage(String imagePath, String userId) async {
    final file = File(imagePath);
    final fileName = '${userId}/${DateTime.now().millisecondsSinceEpoch}_cover.jpg';
    
    await _client.storage.from('podcast-covers').upload(fileName, file);
    return _client.storage.from('podcast-covers').getPublicUrl(fileName);
  }

  /// رفع الملف الصوتي
  Future<String> _uploadAudioFile(String audioPath, String userId, String podcastId) async {
    final file = File(audioPath);
    final fileName = '${userId}/${podcastId}/${DateTime.now().millisecondsSinceEpoch}_episode.mp3';
    
    await _client.storage.from('audio-episodes').upload(fileName, file);
    return _client.storage.from('audio-episodes').getPublicUrl(fileName);
  }

  /// إشعار المتابعين بحلقة جديدة
  Future<void> _notifyFollowersNewEpisode(String episodeId) async {
    try {
      await _client.rpc('notify_followers_new_episode', params: {'p_episode_id': episodeId});
    } catch (e) {
      // تجاهل أخطاء الإشعارات
      print('خطأ في إرسال الإشعارات: $e');
    }
  }

  // =============================================================
  //  التفاعل والمتابعة
  // =============================================================

  /// متابعة بودكاست
  Future<void> followPodcast(String podcastId, {bool enableNotifications = true}) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    await _client.from('audio_podcast_followers').insert({
      'podcast_id': podcastId,
      'user_id': userId,
      'notifications_enabled': enableNotifications,
    });
  }

  /// إلغاء متابعة بودكاست
  Future<void> unfollowPodcast(String podcastId) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    await _client.from('audio_podcast_followers')
        .delete()
        .eq('podcast_id', podcastId)
        .eq('user_id', userId);
  }

  /// تبديل متابعة بودكاست
  Future<bool> togglePodcastFollow(String podcastId) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    final existing = await _client
        .from('audio_podcast_followers')
        .select('id')
        .eq('podcast_id', podcastId)
        .eq('user_id', userId)
        .maybeSingle();

    if (existing != null) {
      await unfollowPodcast(podcastId);
      return false;
    } else {
      await followPodcast(podcastId);
      return true;
    }
  }

  /// إعجاب ببودكاست
  Future<void> likePodcast(String podcastId) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    await _client.from('audio_podcast_likes').insert({
      'podcast_id': podcastId,
      'user_id': userId,
    });
  }

  /// إلغاء إعجاب ببودكاست
  Future<void> unlikePodcast(String podcastId) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    await _client.from('audio_podcast_likes')
        .delete()
        .eq('podcast_id', podcastId)
        .eq('user_id', userId);
  }

  /// تبديل إعجاب ببودكاست
  Future<bool> togglePodcastLike(String podcastId) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    final existing = await _client
        .from('audio_podcast_likes')
        .select('id')
        .eq('podcast_id', podcastId)
        .eq('user_id', userId)
        .maybeSingle();

    if (existing != null) {
      await unlikePodcast(podcastId);
      return false;
    } else {
      await likePodcast(podcastId);
      return true;
    }
  }

  /// إعجاب بحلقة
  Future<void> likeEpisode(String episodeId) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    await _client.from('audio_episode_likes').insert({
      'episode_id': episodeId,
      'user_id': userId,
    });
  }

  /// إلغاء إعجاب بحلقة
  Future<void> unlikeEpisode(String episodeId) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    await _client.from('audio_episode_likes')
        .delete()
        .eq('episode_id', episodeId)
        .eq('user_id', userId);
  }

  /// تبديل إعجاب بحلقة
  Future<bool> toggleEpisodeLike(String episodeId) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    final existing = await _client
        .from('audio_episode_likes')
        .select('id')
        .eq('episode_id', episodeId)
        .eq('user_id', userId)
        .maybeSingle();

    if (existing != null) {
      await unlikeEpisode(episodeId);
      return false;
    } else {
      await likeEpisode(episodeId);
      return true;
    }
  }

  /// إضافة حلقة للمفضلة
  Future<void> favoriteEpisode(String episodeId) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    await _client.from('audio_episode_favorites').insert({
      'episode_id': episodeId,
      'user_id': userId,
    });
  }

  /// إزالة حلقة من المفضلة
  Future<void> unfavoriteEpisode(String episodeId) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    await _client.from('audio_episode_favorites')
        .delete()
        .eq('episode_id', episodeId)
        .eq('user_id', userId);
  }

  /// تبديل إضافة حلقة للمفضلة
  Future<bool> toggleEpisodeFavorite(String episodeId) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    final existing = await _client
        .from('audio_episode_favorites')
        .select('id')
        .eq('episode_id', episodeId)
        .eq('user_id', userId)
        .maybeSingle();

    if (existing != null) {
      await unfavoriteEpisode(episodeId);
      return false;
    } else {
      await favoriteEpisode(episodeId);
      return true;
    }
  }

  /// مشاركة حلقة
  Future<void> shareEpisode(String episodeId, String platform) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    await _client.from('audio_episode_shares').insert({
      'episode_id': episodeId,
      'user_id': userId,
      'platform': platform,
    });
  }
}
