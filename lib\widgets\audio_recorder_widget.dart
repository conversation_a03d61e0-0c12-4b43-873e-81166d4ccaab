import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import 'package:record/record.dart';

class AudioRecorderWidget extends StatefulWidget {
  final Function(String audioPath, int durationSeconds) onRecordingComplete;
  final VoidCallback onCancel;

  const AudioRecorderWidget({
    super.key,
    required this.onRecordingComplete,
    required this.onCancel,
  });

  @override
  State<AudioRecorderWidget> createState() => _AudioRecorderWidgetState();
}

class _AudioRecorderWidgetState extends State<AudioRecorderWidget>
    with TickerProviderStateMixin {
  final AudioRecorder _audioRecorder = AudioRecorder();
  
  bool _isRecording = false;
  bool _isPaused = false;
  bool _hasPermission = false;
  int _recordDuration = 0;
  Timer? _timer;
  String? _audioPath;

  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _checkPermissions();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _animationController.dispose();
    _audioRecorder.dispose();
    super.dispose();
  }

  Future<void> _checkPermissions() async {
    final status = await Permission.microphone.request();
    setState(() {
      _hasPermission = status == PermissionStatus.granted;
    });

    if (!_hasPermission) {
      _showPermissionDialog();
    }
  }

  void _showPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إذن الميكروفون مطلوب'),
        content: const Text(
          'يحتاج التطبيق إلى إذن الوصول للميكروفون لتسجيل الصوت. '
          'يرجى منح الإذن من إعدادات التطبيق.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              widget.onCancel();
            },
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              openAppSettings();
            },
            child: const Text('الإعدادات'),
          ),
        ],
      ),
    );
  }

  Future<void> _startRecording() async {
    if (!_hasPermission) {
      await _checkPermissions();
      return;
    }

    try {
      final directory = await getTemporaryDirectory();
      _audioPath = '${directory.path}/recording_${DateTime.now().millisecondsSinceEpoch}.m4a';

      await _audioRecorder.start(
        const RecordConfig(
          encoder: AudioEncoder.aacLc,
          bitRate: 128000,
          sampleRate: 44100,
        ),
        path: _audioPath!,
      );

      setState(() {
        _isRecording = true;
        _isPaused = false;
        _recordDuration = 0;
      });

      _animationController.repeat(reverse: true);
      _startTimer();
    } catch (e) {
      _showErrorDialog('خطأ في بدء التسجيل: $e');
    }
  }

  Future<void> _pauseRecording() async {
    try {
      await _audioRecorder.pause();
      setState(() {
        _isPaused = true;
      });
      _animationController.stop();
      _timer?.cancel();
    } catch (e) {
      _showErrorDialog('خطأ في إيقاف التسجيل مؤقتاً: $e');
    }
  }

  Future<void> _resumeRecording() async {
    try {
      await _audioRecorder.resume();
      setState(() {
        _isPaused = false;
      });
      _animationController.repeat(reverse: true);
      _startTimer();
    } catch (e) {
      _showErrorDialog('خطأ في استئناف التسجيل: $e');
    }
  }

  Future<void> _stopRecording() async {
    try {
      final path = await _audioRecorder.stop();
      _timer?.cancel();
      _animationController.stop();
      _animationController.reset();

      if (path != null && _recordDuration > 0) {
        widget.onRecordingComplete(path, _recordDuration);
      } else {
        _showErrorDialog('فشل في حفظ التسجيل');
      }
    } catch (e) {
      _showErrorDialog('خطأ في إيقاف التسجيل: $e');
    }
  }

  void _cancelRecording() async {
    try {
      await _audioRecorder.stop();
      _timer?.cancel();
      _animationController.stop();
      _animationController.reset();

      // حذف الملف إذا كان موجوداً
      if (_audioPath != null) {
        final file = File(_audioPath!);
        if (await file.exists()) {
          await file.delete();
        }
      }

      widget.onCancel();
    } catch (e) {
      widget.onCancel();
    }
  }

  void _startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _recordDuration++;
      });
    });
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              widget.onCancel();
            },
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final secs = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    if (!_hasPermission) {
      return Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.red[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.red[200]!),
        ),
        child: Column(
          children: [
            Icon(
              Icons.mic_off,
              size: 48,
              color: Colors.red[600],
            ),
            const SizedBox(height: 16),
            Text(
              'إذن الميكروفون مطلوب',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.red[800],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'يرجى منح إذن الوصول للميكروفون لتتمكن من التسجيل',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.red[600],
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                TextButton(
                  onPressed: widget.onCancel,
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    await _checkPermissions();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('منح الإذن'),
                ),
              ],
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: _isRecording ? Colors.red[50] : Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _isRecording ? Colors.red[200]! : Colors.grey[300]!,
        ),
      ),
      child: Column(
        children: [
          // مؤشر التسجيل
          AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _isRecording && !_isPaused ? _scaleAnimation.value : 1.0,
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: _isRecording 
                        ? (_isPaused ? Colors.orange : Colors.red)
                        : Colors.grey[400],
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _isRecording 
                        ? (_isPaused ? Icons.pause : Icons.mic)
                        : Icons.mic,
                    size: 40,
                    color: Colors.white,
                  ),
                ),
              );
            },
          ),

          const SizedBox(height: 16),

          // مدة التسجيل
          Text(
            _formatDuration(_recordDuration),
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: _isRecording ? Colors.red[800] : Colors.grey[600],
            ),
          ),

          const SizedBox(height: 8),

          Text(
            _isRecording 
                ? (_isPaused ? 'التسجيل متوقف مؤقتاً' : 'جاري التسجيل...')
                : 'اضغط لبدء التسجيل',
            style: TextStyle(
              color: _isRecording ? Colors.red[600] : Colors.grey[600],
              fontSize: 14,
            ),
          ),

          const SizedBox(height: 24),

          // أزرار التحكم
          if (!_isRecording) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                TextButton(
                  onPressed: widget.onCancel,
                  child: const Text('إلغاء'),
                ),
                ElevatedButton.icon(
                  onPressed: _startRecording,
                  icon: const Icon(Icons.mic),
                  label: const Text('بدء التسجيل'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                ),
              ],
            ),
          ] else ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                IconButton(
                  onPressed: _cancelRecording,
                  icon: const Icon(Icons.close),
                  color: Colors.grey[600],
                  iconSize: 32,
                ),
                IconButton(
                  onPressed: _isPaused ? _resumeRecording : _pauseRecording,
                  icon: Icon(_isPaused ? Icons.play_arrow : Icons.pause),
                  color: Colors.orange,
                  iconSize: 32,
                ),
                IconButton(
                  onPressed: _stopRecording,
                  icon: const Icon(Icons.stop),
                  color: Colors.green,
                  iconSize: 32,
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }
}
