-- =============================================================
--  إعداد أساسي لقسم البودكاست - أرزاوو
--  Basic Podcast Setup - Arzawo
-- =============================================================

-- إنشاء جدول البودكاستات الأساسي
CREATE TABLE IF NOT EXISTS audio_podcasts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    creator_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    cover_image_url TEXT,
    category VARCHAR(50) DEFAULT 'general',
    language VARCHAR(10) DEFAULT 'ar',
    privacy_setting VARCHAR(20) DEFAULT 'public',
    content_rating VARCHAR(20) DEFAULT 'general',
    allow_comments BOOLEAN DEFAULT true,
    allow_downloads BOOLEAN DEFAULT true,
    is_featured BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الحلقات الصوتية
CREATE TABLE IF NOT EXISTS audio_episodes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    podcast_id UUID REFERENCES audio_podcasts(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    audio_url TEXT NOT NULL,
    duration_seconds INTEGER DEFAULT 0,
    file_size_bytes BIGINT DEFAULT 0,
    is_published BOOLEAN DEFAULT false,
    publish_at TIMESTAMP WITH TIME ZONE,
    scheduled_for TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول متابعة البودكاستات
CREATE TABLE IF NOT EXISTS audio_podcast_followers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    podcast_id UUID REFERENCES audio_podcasts(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    notifications_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(podcast_id, user_id)
);

-- إنشاء جدول الإعجابات
CREATE TABLE IF NOT EXISTS audio_podcast_likes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    podcast_id UUID REFERENCES audio_podcasts(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(podcast_id, user_id)
);

-- إنشاء جدول إعجابات الحلقات
CREATE TABLE IF NOT EXISTS audio_episode_likes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    episode_id UUID REFERENCES audio_episodes(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(episode_id, user_id)
);

-- إنشاء جدول الاستماع
CREATE TABLE IF NOT EXISTS audio_episode_listens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    episode_id UUID REFERENCES audio_episodes(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    listen_duration INTEGER DEFAULT 0,
    completed BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول التعليقات النصية
CREATE TABLE IF NOT EXISTS audio_episode_text_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    episode_id UUID REFERENCES audio_episodes(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    parent_comment_id UUID REFERENCES audio_episode_text_comments(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول التعليقات الصوتية
CREATE TABLE IF NOT EXISTS audio_episode_voice_comments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    episode_id UUID REFERENCES audio_episodes(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    audio_url TEXT NOT NULL,
    duration_seconds INTEGER DEFAULT 0,
    parent_comment_id UUID REFERENCES audio_episode_voice_comments(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء الفهارس للأداء
CREATE INDEX IF NOT EXISTS idx_audio_podcasts_creator ON audio_podcasts(creator_id);
CREATE INDEX IF NOT EXISTS idx_audio_podcasts_category ON audio_podcasts(category);
CREATE INDEX IF NOT EXISTS idx_audio_podcasts_created_at ON audio_podcasts(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_audio_episodes_podcast ON audio_episodes(podcast_id);
CREATE INDEX IF NOT EXISTS idx_audio_episodes_published ON audio_episodes(is_published, publish_at DESC);
CREATE INDEX IF NOT EXISTS idx_audio_episodes_created_at ON audio_episodes(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_audio_podcast_followers_user ON audio_podcast_followers(user_id);
CREATE INDEX IF NOT EXISTS idx_audio_podcast_followers_podcast ON audio_podcast_followers(podcast_id);

CREATE INDEX IF NOT EXISTS idx_audio_episode_listens_episode ON audio_episode_listens(episode_id);
CREATE INDEX IF NOT EXISTS idx_audio_episode_listens_user ON audio_episode_listens(user_id);

-- إنشاء سياسات الأمان الأساسية
-- سياسات البودكاستات
CREATE POLICY IF NOT EXISTS "Users can view public podcasts" ON audio_podcasts
    FOR SELECT USING (privacy_setting = 'public' OR creator_id = auth.uid());

CREATE POLICY IF NOT EXISTS "Users can create their own podcasts" ON audio_podcasts
    FOR INSERT WITH CHECK (creator_id = auth.uid());

CREATE POLICY IF NOT EXISTS "Users can update their own podcasts" ON audio_podcasts
    FOR UPDATE USING (creator_id = auth.uid());

CREATE POLICY IF NOT EXISTS "Users can delete their own podcasts" ON audio_podcasts
    FOR DELETE USING (creator_id = auth.uid());

-- سياسات الحلقات
CREATE POLICY IF NOT EXISTS "Users can view published episodes" ON audio_episodes
    FOR SELECT USING (
        is_published = true 
        OR EXISTS (
            SELECT 1 FROM audio_podcasts 
            WHERE id = podcast_id AND creator_id = auth.uid()
        )
    );

CREATE POLICY IF NOT EXISTS "Podcast owners can manage episodes" ON audio_episodes
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM audio_podcasts 
            WHERE id = podcast_id AND creator_id = auth.uid()
        )
    );

-- سياسات المتابعة والإعجابات
CREATE POLICY IF NOT EXISTS "Users can manage their own follows" ON audio_podcast_followers
    FOR ALL USING (user_id = auth.uid());

CREATE POLICY IF NOT EXISTS "Users can manage their own likes" ON audio_podcast_likes
    FOR ALL USING (user_id = auth.uid());

CREATE POLICY IF NOT EXISTS "Users can manage their own episode likes" ON audio_episode_likes
    FOR ALL USING (user_id = auth.uid());

-- سياسات الاستماع
CREATE POLICY IF NOT EXISTS "Users can track their own listens" ON audio_episode_listens
    FOR ALL USING (user_id = auth.uid() OR user_id IS NULL);

-- سياسات التعليقات
CREATE POLICY IF NOT EXISTS "Users can view all comments" ON audio_episode_text_comments
    FOR SELECT USING (true);

CREATE POLICY IF NOT EXISTS "Users can manage their own text comments" ON audio_episode_text_comments
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY IF NOT EXISTS "Users can update their own text comments" ON audio_episode_text_comments
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY IF NOT EXISTS "Users can delete their own text comments" ON audio_episode_text_comments
    FOR DELETE USING (user_id = auth.uid());

CREATE POLICY IF NOT EXISTS "Users can view all voice comments" ON audio_episode_voice_comments
    FOR SELECT USING (true);

CREATE POLICY IF NOT EXISTS "Users can manage their own voice comments" ON audio_episode_voice_comments
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY IF NOT EXISTS "Users can delete their own voice comments" ON audio_episode_voice_comments
    FOR DELETE USING (user_id = auth.uid());

-- تفعيل RLS على جميع الجداول
ALTER TABLE audio_podcasts ENABLE ROW LEVEL SECURITY;
ALTER TABLE audio_episodes ENABLE ROW LEVEL SECURITY;
ALTER TABLE audio_podcast_followers ENABLE ROW LEVEL SECURITY;
ALTER TABLE audio_podcast_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE audio_episode_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE audio_episode_listens ENABLE ROW LEVEL SECURITY;
ALTER TABLE audio_episode_text_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE audio_episode_voice_comments ENABLE ROW LEVEL SECURITY;

-- إدراج بيانات تجريبية (اختيارية)
INSERT INTO audio_podcasts (creator_id, name, description, category, language) 
VALUES 
    (auth.uid(), 'بودكاست تجريبي', 'هذا بودكاست تجريبي لاختبار النظام', 'general', 'ar')
ON CONFLICT DO NOTHING;

-- رسالة نجاح
SELECT 'تم إعداد قسم البودكاست بنجاح! يمكنك الآن استخدام جميع المميزات.' as message;
