import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/audio_podcaster.dart';
import '../models/audio_podcast_category.dart';

class AudioAnalyticsService {
  final SupabaseClient _client = Supabase.instance.client;

  String? get currentUserId => _client.auth.currentUser?.id;

  // =============================================================
  //  تسجيل الإحصائيات
  // =============================================================

  /// تسجيل استماع جديد
  Future<void> logEpisodeListen({
    required String episodeId,
    int listenDurationSeconds = 0,
    double completionPercentage = 0.0,
    String? deviceType,
    String? ipAddress,
    String? userAgent,
  }) async {
    final userId = currentUserId; // يمكن أن يكون null للزوار

    await _client.rpc('log_episode_listen', params: {
      'p_episode_id': episodeId,
      'p_user_id': userId,
      'p_duration_seconds': listenDurationSeconds,
      'p_completion_percentage': completionPercentage,
      'p_device_type': deviceType,
      'p_ip_address': ipAddress,
      'p_user_agent': userAgent,
    });
  }

  /// تسجيل تحميل حلقة
  Future<void> logEpisodeDownload({
    required String episodeId,
    AudioDownloadQuality quality = AudioDownloadQuality.medium,
    int? fileSizeBytes,
    DateTime? expiresAt,
  }) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    await _client.from('audio_episode_downloads').insert({
      'episode_id': episodeId,
      'user_id': userId,
      'download_quality': quality.value,
      'file_size_bytes': fileSizeBytes,
      'download_completed': true,
      'expires_at': expiresAt?.toIso8601String(),
    });
  }

  /// تحديث حالة التحميل
  Future<void> updateDownloadStatus(String downloadId, bool completed) async {
    await _client.from('audio_episode_downloads')
        .update({'download_completed': completed})
        .eq('id', downloadId);
  }

  // =============================================================
  //  إحصائيات البودكاستر
  // =============================================================

  /// جلب إحصائيات البودكاستر
  Future<AudioPodcasterStats> getPodcasterStats(String userId) async {
    final response = await _client.rpc('get_podcaster_stats', params: {
      'p_user_id': userId,
    }).single();

    return AudioPodcasterStats.fromJson(response);
  }

  /// جلب إحصائيات البودكاست
  Future<Map<String, dynamic>> getPodcastStats(String podcastId) async {
    final response = await _client
        .from('audio_podcasts')
        .select('total_episodes, total_listens, total_followers, total_likes')
        .eq('id', podcastId)
        .single();

    return response;
  }

  /// جلب إحصائيات الحلقة
  Future<Map<String, dynamic>> getEpisodeStats(String episodeId) async {
    final response = await _client
        .from('audio_episodes')
        .select('listens_count, likes_count, comments_count, shares_count, downloads_count')
        .eq('id', episodeId)
        .single();

    return response;
  }

  /// جلب إحصائيات الاستماع التفصيلية للحلقة
  Future<List<AudioListenStats>> getEpisodeListenStats(String episodeId) async {
    final response = await _client
        .from('audio_episode_listens')
        .select('*')
        .eq('episode_id', episodeId)
        .order('created_at', ascending: false);

    return (response as List).map((json) => AudioListenStats.fromJson(json)).toList();
  }

  /// جلب إحصائيات الاستماع للبودكاستر
  Future<Map<String, dynamic>> getPodcasterListenStats(String userId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    var query = _client
        .from('audio_episode_listens')
        .select('''
          listen_duration_seconds,
          completion_percentage,
          device_type,
          created_at,
          audio_episodes!inner(
            podcast_id,
            audio_podcasts!inner(creator_id)
          )
        ''')
        .eq('audio_episodes.audio_podcasts.creator_id', userId);

    if (startDate != null) {
      query = query.gte('created_at', startDate.toIso8601String());
    }
    if (endDate != null) {
      query = query.lte('created_at', endDate.toIso8601String());
    }

    final response = await query;
    
    // تحليل البيانات
    int totalListens = response.length;
    int totalDuration = 0;
    double avgCompletion = 0.0;
    Map<String, int> deviceStats = {};

    for (final listen in response) {
      totalDuration += listen['listen_duration_seconds'] as int;
      avgCompletion += listen['completion_percentage'] as double;
      
      final device = listen['device_type'] as String? ?? 'unknown';
      deviceStats[device] = (deviceStats[device] ?? 0) + 1;
    }

    if (totalListens > 0) {
      avgCompletion /= totalListens;
    }

    return {
      'total_listens': totalListens,
      'total_duration_seconds': totalDuration,
      'average_completion_percentage': avgCompletion,
      'device_stats': deviceStats,
    };
  }

  // =============================================================
  //  إحصائيات عامة
  // =============================================================

  /// جلب أشهر البودكاستات
  Future<List<Map<String, dynamic>>> getTopPodcasts({
    int limit = 10,
    String period = 'all_time', // all_time, month, week
  }) async {
    var query = _client
        .from('audio_podcasts')
        .select('id, name, total_listens, total_followers, creator_id')
        .eq('is_active', true)
        .eq('privacy_setting', 'public')
        .order('total_listens', ascending: false)
        .limit(limit);

    final response = await query;
    return List<Map<String, dynamic>>.from(response);
  }

  /// جلب أشهر الحلقات
  Future<List<Map<String, dynamic>>> getTopEpisodes({
    int limit = 10,
    String period = 'all_time',
  }) async {
    var query = _client
        .from('audio_episodes')
        .select('''
          id, title, listens_count, likes_count,
          audio_podcasts!inner(name, creator_id, privacy_setting)
        ''')
        .eq('is_published', true)
        .eq('audio_podcasts.privacy_setting', 'public')
        .order('listens_count', ascending: false)
        .limit(limit);

    final response = await query;
    return List<Map<String, dynamic>>.from(response);
  }

  /// جلب إحصائيات الفئات
  Future<List<Map<String, dynamic>>> getCategoryStats() async {
    final response = await _client
        .from('audio_podcasts')
        .select('category')
        .eq('is_active', true)
        .eq('privacy_setting', 'public');

    // تجميع البيانات حسب الفئة
    Map<String, int> categoryCount = {};
    for (final podcast in response) {
      final category = podcast['category'] as String;
      categoryCount[category] = (categoryCount[category] ?? 0) + 1;
    }

    // تحويل إلى قائمة مرتبة
    final sortedCategories = categoryCount.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedCategories.map((entry) => {
      'category': entry.key,
      'count': entry.value,
    }).toList();
  }

  /// جلب إحصائيات النمو
  Future<Map<String, dynamic>> getGrowthStats({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final now = DateTime.now();
    final start = startDate ?? now.subtract(const Duration(days: 30));
    final end = endDate ?? now;

    // إحصائيات البودكاستات الجديدة
    final newPodcasts = await _client
        .from('audio_podcasts')
        .select('id')
        .gte('created_at', start.toIso8601String())
        .lte('created_at', end.toIso8601String());

    // إحصائيات الحلقات الجديدة
    final newEpisodes = await _client
        .from('audio_episodes')
        .select('id')
        .gte('created_at', start.toIso8601String())
        .lte('created_at', end.toIso8601String());

    // إحصائيات الاستماعات
    final totalListens = await _client
        .from('audio_episode_listens')
        .select('id')
        .gte('created_at', start.toIso8601String())
        .lte('created_at', end.toIso8601String());

    return {
      'new_podcasts': newPodcasts.length,
      'new_episodes': newEpisodes.length,
      'total_listens': totalListens.length,
      'period_start': start.toIso8601String(),
      'period_end': end.toIso8601String(),
    };
  }

  // =============================================================
  //  إحصائيات المستخدم
  // =============================================================

  /// جلب الحلقات المفضلة للمستخدم
  Future<List<Map<String, dynamic>>> getUserFavoriteEpisodes() async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    final response = await _client
        .from('audio_episode_favorites')
        .select('''
          created_at,
          audio_episodes!inner(
            id, title, duration_seconds,
            audio_podcasts!inner(name, creator_id)
          )
        ''')
        .eq('user_id', userId)
        .order('created_at', ascending: false);

    return List<Map<String, dynamic>>.from(response);
  }

  /// جلب البودكاستات المتابعة للمستخدم
  Future<List<Map<String, dynamic>>> getUserFollowedPodcasts() async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    final response = await _client
        .from('audio_podcast_followers')
        .select('''
          created_at, notifications_enabled,
          audio_podcasts!inner(
            id, name, total_episodes, total_listens, creator_id
          )
        ''')
        .eq('user_id', userId)
        .order('created_at', ascending: false);

    return List<Map<String, dynamic>>.from(response);
  }

  /// جلب تاريخ الاستماع للمستخدم
  Future<List<Map<String, dynamic>>> getUserListenHistory({int limit = 50}) async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    final response = await _client
        .from('audio_episode_listens')
        .select('''
          created_at, listen_duration_seconds, completion_percentage,
          audio_episodes!inner(
            id, title, duration_seconds,
            audio_podcasts!inner(name, creator_id)
          )
        ''')
        .eq('user_id', userId)
        .order('created_at', ascending: false)
        .limit(limit);

    return List<Map<String, dynamic>>.from(response);
  }

  /// جلب التحميلات المحفوظة للمستخدم
  Future<List<AudioEpisodeDownload>> getUserDownloads() async {
    final userId = currentUserId;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    final response = await _client
        .from('audio_episode_downloads')
        .select('*')
        .eq('user_id', userId)
        .eq('download_completed', true)
        .order('created_at', ascending: false);

    return (response as List).map((json) => AudioEpisodeDownload.fromJson(json)).toList();
  }
}
