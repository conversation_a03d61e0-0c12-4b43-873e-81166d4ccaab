import 'package:flutter/material.dart';
import '../models/audio_episode.dart';
import '../models/audio_comment.dart';
import '../services/audio_comment_service.dart';
import '../widgets/add_comment_widget.dart';
import '../widgets/comment_item_widget.dart';
import '../widgets/voice_comment_item_widget.dart';

class EpisodeCommentsSection extends StatefulWidget {
  final AudioEpisode episode;
  final List<AudioEpisodeComment> textComments;
  final List<AudioEpisodeVoiceComment> voiceComments;
  final VoidCallback onCommentAdded;

  const EpisodeCommentsSection({
    super.key,
    required this.episode,
    required this.textComments,
    required this.voiceComments,
    required this.onCommentAdded,
  });

  @override
  State<EpisodeCommentsSection> createState() => _EpisodeCommentsSectionState();
}

class _EpisodeCommentsSectionState extends State<EpisodeCommentsSection>
    with SingleTickerProviderStateMixin {
  final _commentService = AudioCommentService();
  late TabController _commentTabController;
  bool _isAddingComment = false;

  @override
  void initState() {
    super.initState();
    _commentTabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _commentTabController.dispose();
    super.dispose();
  }

  Future<void> _addTextComment(String content, {String? parentId}) async {
    setState(() {
      _isAddingComment = true;
    });

    try {
      await _commentService.addTextComment(
        episodeId: widget.episode.id,
        content: content,
        parentCommentId: parentId,
      );
      
      widget.onCommentAdded();
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إضافة التعليق بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في إضافة التعليق: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isAddingComment = false;
      });
    }
  }

  Future<void> _addVoiceComment(String audioPath, int duration) async {
    setState(() {
      _isAddingComment = true;
    });

    try {
      await _commentService.addVoiceComment(
        episodeId: widget.episode.id,
        audioPath: audioPath,
        durationSeconds: duration,
      );
      
      widget.onCommentAdded();
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إضافة التعليق الصوتي بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في إضافة التعليق الصوتي: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isAddingComment = false;
      });
    }
  }

  Future<void> _toggleCommentLike(String commentId, bool isVoice) async {
    try {
      if (isVoice) {
        await _commentService.toggleVoiceCommentLike(commentId);
      } else {
        await _commentService.toggleTextCommentLike(commentId);
      }
      widget.onCommentAdded(); // إعادة تحميل التعليقات
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في الإعجاب: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final totalComments = widget.textComments.length + widget.voiceComments.length;

    return Column(
      children: [
        // إحصائيات التعليقات
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(Icons.comment, color: Colors.grey[600]),
              const SizedBox(width: 8),
              Text(
                '$totalComments تعليق',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              TextButton.icon(
                onPressed: () {
                  _showAddCommentDialog();
                },
                icon: const Icon(Icons.add),
                label: const Text('إضافة تعليق'),
              ),
            ],
          ),
        ),

        // تبويبات التعليقات
        TabBar(
          controller: _commentTabController,
          labelColor: Colors.deepPurple,
          unselectedLabelColor: Colors.grey[600],
          indicatorColor: Colors.deepPurple,
          tabs: [
            Tab(text: 'نصية (${widget.textComments.length})'),
            Tab(text: 'صوتية (${widget.voiceComments.length})'),
          ],
        ),

        // محتوى التبويبات
        Expanded(
          child: TabBarView(
            controller: _commentTabController,
            children: [
              // التعليقات النصية
              _buildTextComments(),
              
              // التعليقات الصوتية
              _buildVoiceComments(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTextComments() {
    if (widget.textComments.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.comment_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'لا توجد تعليقات نصية بعد',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'كن أول من يعلق على هذه الحلقة',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: widget.textComments.length,
      itemBuilder: (context, index) {
        final comment = widget.textComments[index];
        return CommentItemWidget(
          comment: comment,
          onLike: () => _toggleCommentLike(comment.id, false),
          onReply: (content) => _addTextComment(content, parentId: comment.id),
        );
      },
    );
  }

  Widget _buildVoiceComments() {
    if (widget.voiceComments.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.mic_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'لا توجد تعليقات صوتية بعد',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'سجل أول تعليق صوتي على هذه الحلقة',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: widget.voiceComments.length,
      itemBuilder: (context, index) {
        final comment = widget.voiceComments[index];
        return VoiceCommentItemWidget(
          comment: comment,
          onLike: () => _toggleCommentLike(comment.id, true),
        );
      },
    );
  }

  void _showAddCommentDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: AddCommentWidget(
          episodeId: widget.episode.id,
          onTextCommentAdded: _addTextComment,
          onVoiceCommentAdded: _addVoiceComment,
          isLoading: _isAddingComment,
        ),
      ),
    );
  }
}


