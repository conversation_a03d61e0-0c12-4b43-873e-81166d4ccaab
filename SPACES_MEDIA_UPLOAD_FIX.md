# إصلاح مشكلة رفع الوسائط في المساحات
# Fix Spaces Media Upload Issue

## 🚨 المشكلة
عند محاولة نشر صورة أو فيديو في المساحات، يظهر الخطأ:
```
StorageException(message: Bucket not found, statusCode: 404, error: Bucket not found)
```

## 🔍 السبب
التطبيق كان يحاول رفع الوسائط إلى buckets غير موجودة:
- `posts-images` - للمساحات
- `community-images` - للمجتمعات

## ✅ الحل المطبق

### 1. إصلاح new_space_post_sheet.dart
```dart
// قبل الإصلاح
final url = await supabaseService.uploadSpaceImage(bytes, fileName);

// بعد الإصلاح
final url = await supabaseService.uploadMedia(bytes, fileName);
```

### 2. إصلاح uploadSpaceImage في supabase_service.dart
```dart
// قبل الإصلاح
await _client.storage.from('posts-images').uploadBinary(...)

// بعد الإصلاح
await _client.storage.from('media').uploadBinary(...)
```

### 3. إصلاح uploadCommunityImage في supabase_service.dart
```dart
// قبل الإصلاح
await _client.storage.from('community-images').uploadBinary(...)

// بعد الإصلاح
await _client.storage.from('media').uploadBinary(...)
```

## 🛠️ الملفات المعدلة

1. **lib/widgets/new_space_post_sheet.dart**
   - تغيير من `uploadSpaceImage` إلى `uploadMedia`

2. **lib/supabase_service.dart**
   - إصلاح `uploadSpaceImage` لاستخدام bucket `media`
   - إصلاح `uploadCommunityImage` لاستخدام bucket `media`

3. **FIX_STORAGE_BUCKETS.sql**
   - سكريپت للتحقق من buckets وإنشاء المفقودة

## 🎯 النتائج المتوقعة

بعد تطبيق الإصلاحات:
- ✅ رفع الصور في المساحات يعمل بشكل صحيح
- ✅ رفع الفيديوهات في المساحات يعمل بشكل صحيح
- ✅ رفع صور المجتمعات يعمل بشكل صحيح
- ✅ جميع الوسائط تُحفظ في bucket `media` الموحد

## 📋 خطوات الاختبار

### اختبار 1: رفع صورة في المساحة
1. افتح التطبيق
2. اذهب إلى "مساحتي"
3. ادخل إلى مساحة موجودة
4. اضغط "منشور جديد"
5. اختر صورة
6. اضغط "نشر"
7. ✅ يجب أن تُرفع الصورة بنجاح

### اختبار 2: رفع صور متعددة في المساحة
1. كرر الخطوات السابقة
2. اختر 2-4 صور
3. ✅ يجب أن تُرفع جميع الصور بنجاح

### اختبار 3: رفع فيديو في المساحة
1. كرر الخطوات السابقة
2. اختر فيديو
3. ✅ يجب أن يُرفع الفيديو بنجاح

## 🔧 إعداد قاعدة البيانات

قم بتشغيل `FIX_STORAGE_BUCKETS.sql` في Supabase SQL Editor:
```sql
-- سيتحقق من وجود bucket 'media'
-- وينشئه إذا لم يكن موجوداً
-- ويعرض إحصائيات buckets
```

## 🚀 بناء التطبيق

بعد تطبيق الإصلاحات:
```bash
flutter clean
flutter pub get
flutter build apk --release
```

## 📝 ملاحظات مهمة

1. **التوحيد**: جميع الوسائط الآن تُحفظ في bucket `media` واحد
2. **البساطة**: لا حاجة لإنشاء buckets متعددة
3. **الصيانة**: أسهل في الإدارة والصيانة
4. **الأمان**: نفس مستوى الأمان مع تبسيط البنية

## 🎉 الميزات المدعومة الآن

- ✅ رفع الصور في المساحات
- ✅ رفع الفيديوهات في المساحات
- ✅ رفع الصور المتعددة في المساحات
- ✅ رفع صور المجتمعات (avatar/cover)
- ✅ عرض الوسائط بشكل صحيح
- ✅ حفظ URLs في قاعدة البيانات

## 🔍 استكشاف الأخطاء

إذا استمرت المشكلة:

1. **تحقق من bucket media:**
   ```sql
   SELECT * FROM storage.buckets WHERE id = 'media';
   ```

2. **تحقق من الصلاحيات:**
   ```sql
   SELECT * FROM storage.objects WHERE bucket_id = 'media' LIMIT 5;
   ```

3. **تحقق من logs التطبيق:**
   ابحث عن رسائل DEBUG في console

4. **اختبر الرفع يدوياً:**
   جرب رفع ملف من Supabase Dashboard

## 📱 APK الجديد

بعد بناء التطبيق، ستحصل على APK محدث في:
`build\app\outputs\flutter-apk\app-release.apk`

الآن يجب أن تعمل جميع وظائف رفع الوسائط في المساحات والمجتمعات بشكل مثالي!
