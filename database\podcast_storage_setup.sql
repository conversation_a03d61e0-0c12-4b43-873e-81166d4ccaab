-- =============================================================
--  إعداد Storage للبودكاست - أرزاوو
--  Podcast Storage Setup - Arzawo
-- =============================================================

-- 1. إنشاء bucket للملفات الصوتية
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'audio-episodes',
    'audio-episodes',
    false, -- خاص للحماية
    104857600, -- 100MB حد أقصى
    ARRAY['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/m4a', 'audio/aac']
)
ON CONFLICT (id) DO NOTHING;

-- 2. إنشاء bucket لصور أغلفة البودكاست
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'podcast-covers',
    'podcast-covers',
    true, -- عام للعرض
    5242880, -- 5MB حد أقصى
    ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
)
ON CONFLICT (id) DO NOTHING;

-- 3. إنشاء bucket للتعليقات الصوتية
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'voice-comments',
    'voice-comments',
    false, -- خاص للحماية
    10485760, -- 10MB حد أقصى
    ARRAY['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/m4a', 'audio/aac']
)
ON CONFLICT (id) DO NOTHING;

-- 4. سياسات رفع الملفات الصوتية (للمستخدمين المسجلين فقط)
CREATE POLICY IF NOT EXISTS "Authenticated users can upload audio episodes" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'audio-episodes' AND 
        auth.role() = 'authenticated'
    );

-- 5. سياسة قراءة الملفات الصوتية (للمستخدمين المسجلين فقط)
CREATE POLICY IF NOT EXISTS "Authenticated users can view audio episodes" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'audio-episodes' AND 
        auth.role() = 'authenticated'
    );

-- 6. سياسة حذف الملفات الصوتية (لمالك الملف فقط)
CREATE POLICY IF NOT EXISTS "Users can delete their own audio episodes" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'audio-episodes' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- 7. سياسات رفع صور الأغلفة (للمستخدمين المسجلين فقط)
CREATE POLICY IF NOT EXISTS "Authenticated users can upload podcast covers" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'podcast-covers' AND 
        auth.role() = 'authenticated'
    );

-- 8. سياسة قراءة صور الأغلفة (للجميع)
CREATE POLICY IF NOT EXISTS "Anyone can view podcast covers" ON storage.objects
    FOR SELECT USING (bucket_id = 'podcast-covers');

-- 9. سياسة حذف صور الأغلفة (لمالك الملف فقط)
CREATE POLICY IF NOT EXISTS "Users can delete their own podcast covers" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'podcast-covers' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- 10. سياسات رفع التعليقات الصوتية (للمستخدمين المسجلين فقط)
CREATE POLICY IF NOT EXISTS "Authenticated users can upload voice comments" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'voice-comments' AND 
        auth.role() = 'authenticated'
    );

-- 11. سياسة قراءة التعليقات الصوتية (للمستخدمين المسجلين فقط)
CREATE POLICY IF NOT EXISTS "Authenticated users can view voice comments" ON storage.objects
    FOR SELECT USING (
        bucket_id = 'voice-comments' AND 
        auth.role() = 'authenticated'
    );

-- 12. سياسة حذف التعليقات الصوتية (لمالك الملف فقط)
CREATE POLICY IF NOT EXISTS "Users can delete their own voice comments" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'voice-comments' AND 
        auth.uid()::text = (storage.foldername(name))[1]
    );

-- رسالة نجاح
SELECT 'تم إعداد Storage للبودكاست بنجاح! يمكنك الآن رفع الملفات الصوتية والصور.' as message;
