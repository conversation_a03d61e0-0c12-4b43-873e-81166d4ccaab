import 'audio_podcast.dart';

/// نموذج فئة البودكاست
class AudioPodcastCategoryModel {
  final String id;
  final String nameAr;
  final String nameEn;
  final String? description;
  final String? icon;
  final String? color;
  final bool isActive;
  final int sortOrder;

  const AudioPodcastCategoryModel({
    required this.id,
    required this.nameAr,
    required this.nameEn,
    this.description,
    this.icon,
    this.color,
    this.isActive = true,
    this.sortOrder = 0,
  });

  factory AudioPodcastCategoryModel.fromJson(Map<String, dynamic> json) {
    return AudioPodcastCategoryModel(
      id: json['id'] ?? '',
      nameAr: json['name_ar'] ?? '',
      nameEn: json['name_en'] ?? '',
      description: json['description'],
      icon: json['icon'],
      color: json['color'],
      isActive: json['is_active'] ?? true,
      sortOrder: json['sort_order'] ?? 0,
    );
  }

  Map<String, dynamic> to<PERSON>son() {
    return {
      'id': id,
      'name_ar': nameAr,
      'name_en': nameEn,
      'description': description,
      'icon': icon,
      'color': color,
      'is_active': isActive,
      'sort_order': sortOrder,
    };
  }

  AudioPodcastCategoryModel copyWith({
    String? id,
    String? nameAr,
    String? nameEn,
    String? description,
    String? icon,
    String? color,
    bool? isActive,
    int? sortOrder,
  }) {
    return AudioPodcastCategoryModel(
      id: id ?? this.id,
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      description: description ?? this.description,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      isActive: isActive ?? this.isActive,
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }

  String get displayName => nameAr;
  bool get hasIcon => icon != null && icon!.isNotEmpty;
  bool get hasColor => color != null && color!.isNotEmpty;
  bool get hasDescription => description != null && description!.isNotEmpty;
}

/// نموذج إحصائيات البحث والفلترة
class AudioPodcastSearchFilters {
  final String query;
  final String? categoryId;
  final String? language;
  final AudioContentRating? contentRating;
  final AudioPodcastSortBy sortBy;
  final int limit;
  final int offset;

  const AudioPodcastSearchFilters({
    this.query = '',
    this.categoryId,
    this.language,
    this.contentRating,
    this.sortBy = AudioPodcastSortBy.popularity,
    this.limit = 20,
    this.offset = 0,
  });

  AudioPodcastSearchFilters copyWith({
    String? query,
    String? categoryId,
    String? language,
    AudioContentRating? contentRating,
    AudioPodcastSortBy? sortBy,
    int? limit,
    int? offset,
  }) {
    return AudioPodcastSearchFilters(
      query: query ?? this.query,
      categoryId: categoryId ?? this.categoryId,
      language: language ?? this.language,
      contentRating: contentRating ?? this.contentRating,
      sortBy: sortBy ?? this.sortBy,
      limit: limit ?? this.limit,
      offset: offset ?? this.offset,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'query': query,
      'category_id': categoryId,
      'language': language,
      'content_rating': contentRating?.value,
      'sort_by': sortBy.value,
      'limit': limit,
      'offset': offset,
    };
  }

  bool get hasQuery => query.isNotEmpty;
  bool get hasFilters => categoryId != null || language != null || contentRating != null;
}

/// ترتيب البودكاستات
enum AudioPodcastSortBy {
  popularity('popularity', 'الأكثر شعبية'),
  newest('newest', 'الأحدث'),
  oldest('oldest', 'الأقدم'),
  mostFollowed('most_followed', 'الأكثر متابعة'),
  mostLiked('most_liked', 'الأكثر إعجاباً'),
  alphabetical('alphabetical', 'أبجدياً');

  const AudioPodcastSortBy(this.value, this.nameAr);

  final String value;
  final String nameAr;

  static AudioPodcastSortBy fromString(String value) {
    return AudioPodcastSortBy.values.firstWhere(
      (sort) => sort.value == value,
      orElse: () => AudioPodcastSortBy.popularity,
    );
  }
}

/// نموذج إحصائيات الاستماع
class AudioListenStats {
  final String id;
  final String episodeId;
  final String? userId;
  final int listenDurationSeconds;
  final double completionPercentage;
  final String? deviceType;
  final String? ipAddress;
  final String? userAgent;
  final DateTime createdAt;

  const AudioListenStats({
    required this.id,
    required this.episodeId,
    this.userId,
    this.listenDurationSeconds = 0,
    this.completionPercentage = 0.0,
    this.deviceType,
    this.ipAddress,
    this.userAgent,
    required this.createdAt,
  });

  factory AudioListenStats.fromJson(Map<String, dynamic> json) {
    return AudioListenStats(
      id: json['id'] ?? '',
      episodeId: json['episode_id'] ?? '',
      userId: json['user_id'],
      listenDurationSeconds: json['listen_duration_seconds'] ?? 0,
      completionPercentage: (json['completion_percentage'] ?? 0.0).toDouble(),
      deviceType: json['device_type'],
      ipAddress: json['ip_address'],
      userAgent: json['user_agent'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'episode_id': episodeId,
      'user_id': userId,
      'listen_duration_seconds': listenDurationSeconds,
      'completion_percentage': completionPercentage,
      'device_type': deviceType,
      'ip_address': ipAddress,
      'user_agent': userAgent,
      'created_at': createdAt.toIso8601String(),
    };
  }

  String get formattedDuration {
    final hours = listenDurationSeconds ~/ 3600;
    final minutes = (listenDurationSeconds % 3600) ~/ 60;
    final seconds = listenDurationSeconds % 60;

    if (hours > 0) {
      return '${hours}س ${minutes}د ${seconds}ث';
    } else if (minutes > 0) {
      return '${minutes}د ${seconds}ث';
    } else {
      return '${seconds}ث';
    }
  }

  String get completionText {
    return '${completionPercentage.toStringAsFixed(1)}%';
  }

  bool get isCompleted => completionPercentage >= 90.0;
  bool get isPartiallyListened => completionPercentage >= 10.0 && completionPercentage < 90.0;
  bool get isSkipped => completionPercentage < 10.0;
}

/// نموذج التحميل للاستماع دون إنترنت
class AudioEpisodeDownload {
  final String id;
  final String episodeId;
  final String userId;
  final AudioDownloadQuality downloadQuality;
  final int? fileSizeBytes;
  final bool downloadCompleted;
  final DateTime? expiresAt;
  final DateTime createdAt;

  const AudioEpisodeDownload({
    required this.id,
    required this.episodeId,
    required this.userId,
    this.downloadQuality = AudioDownloadQuality.medium,
    this.fileSizeBytes,
    this.downloadCompleted = false,
    this.expiresAt,
    required this.createdAt,
  });

  factory AudioEpisodeDownload.fromJson(Map<String, dynamic> json) {
    return AudioEpisodeDownload(
      id: json['id'] ?? '',
      episodeId: json['episode_id'] ?? '',
      userId: json['user_id'] ?? '',
      downloadQuality: AudioDownloadQuality.fromString(json['download_quality'] ?? 'medium'),
      fileSizeBytes: json['file_size_bytes'],
      downloadCompleted: json['download_completed'] ?? false,
      expiresAt: json['expires_at'] != null ? DateTime.parse(json['expires_at']) : null,
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'episode_id': episodeId,
      'user_id': userId,
      'download_quality': downloadQuality.value,
      'file_size_bytes': fileSizeBytes,
      'download_completed': downloadCompleted,
      'expires_at': expiresAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
    };
  }

  bool get isExpired => expiresAt != null && expiresAt!.isBefore(DateTime.now());
  bool get isValid => downloadCompleted && !isExpired;

  String get statusText {
    if (!downloadCompleted) return 'جاري التحميل';
    if (isExpired) return 'منتهي الصلاحية';
    return 'متاح';
  }
}

/// جودة التحميل
enum AudioDownloadQuality {
  low('low', 'منخفضة'),
  medium('medium', 'متوسطة'),
  high('high', 'عالية');

  const AudioDownloadQuality(this.value, this.nameAr);

  final String value;
  final String nameAr;

  static AudioDownloadQuality fromString(String value) {
    return AudioDownloadQuality.values.firstWhere(
      (quality) => quality.value == value,
      orElse: () => AudioDownloadQuality.medium,
    );
  }
}


